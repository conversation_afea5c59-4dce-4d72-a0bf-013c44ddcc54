import { db } from "./firebase.ts";

async function main() {
    const usersRef = db.collection('users');

    // create completedQuestions subcollection if not exists
    const snapshot = await usersRef.get();
    snapshot.forEach(async (doc) => {
        const userRef = usersRef.doc(doc.id);
        const completedQuestionsRef = userRef.collection('completedQuestions');
        const completedQuestionsSnapshot = await completedQuestionsRef.get();
        if (completedQuestionsSnapshot.empty) {
            const completedQuestionsRef = userRef.collection('completedQuestions').doc('dataDoc');
            // Create stats object mapping all question types to { total: 0, correct: 0 }
            const questionTypes: QuestionType[] = [
                "Word in Context",
                "Main Purpose Underlined",
                "Main Idea",
                "Main Purpose",
                "Overall Structure",
                "Specific Detail",
                "Command of Evidence",
                "Paired Passage",
                "Inference"
            ];
            const stats = Object.fromEntries(
                questionTypes.map(type => [type, { total: 0, correct: 0 }])
            );
            await completedQuestionsRef.set({
                data: [],
                stats,
                markedQuestions: [],
                incorrectlyAnsweredQuestions: []
            });
        }
    });



}