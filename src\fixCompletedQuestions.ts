import { db } from "./firebase.ts";

async function main() {
    const usersRef = db.collection('users');

    // create completedQuestions subcollection if not exists
    const snapshot = await usersRef.get();

    // Use Promise.all to process all users in parallel
    const promises = snapshot.docs.map(async (doc) => {
        const userRef = usersRef.doc(doc.id);
        const completedQuestionsRef = userRef.collection('completedQuestions');
        const completedQuestionsSnapshot = await completedQuestionsRef.get();

        if (completedQuestionsSnapshot.empty) {
            const completedQuestionsDocRef = userRef.collection('completedQuestions').doc('dataDoc');
            // Create stats object mapping all question types to { total: 0, correct: 0 }
            const questionTypes = [
                "Word in Context",
                "Main Purpose Underlined",
                "Main Idea",
                "Main Purpose",
                "Overall Structure",
                "Specific Detail",
                "Command of Evidence",
                "Paired Passage",
                "Inference"
            ];
            const stats = Object.fromEntries(
                questionTypes.map(type => [type, { total: 0, correct: 0 }])
            );
            await completedQuestionsDocRef.set({
                data: [],
                stats,
                markedQuestions: [],
                incorrectlyAnsweredQuestions: []
            });
        }
    });

    // Wait for all operations to complete
    await Promise.all(promises);
    console.log(`Processed ${promises.length} users`);
}